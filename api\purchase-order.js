const express = require('express');
const router = express.Router();
const { Pool } = require('pg');
const crypto = require('crypto');
const { authenticateAdminToken, authenticateStaffToken } = require('./auth');
const tpbankAPI = require('../api_tpbank/index');
require('dotenv').config();

// Kết nối database
const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'tracking_db',
    password: process.env.DB_PASSWORD || 'England1@',
    port: process.env.DB_PORT || 5432,
});

// Hàm đồng bộ trạng thái tracking cho đơn hàng
async function syncPurchaseOrderTrackingStatus(purchaseOrderId) {
    try {
        // <PERSON><PERSON><PERSON><PERSON> s<PERSON>ch tracking của đơn mua hộ
        const trackingsResult = await pool.query(`
            SELECT pot.*, t.status as tracking_status
            FROM purchase_order_tracking pot
            JOIN purchase_order_items poi ON pot.purchase_order_item_id = poi.id
            LEFT JOIN tracking t ON pot.tracking_number = t.number
            WHERE poi.purchase_order_id = $1
        `, [purchaseOrderId]);

        // Nếu không có tracking, không cần đồng bộ
        if (trackingsResult.rows.length === 0) {
            console.log(`Đơn hàng ${purchaseOrderId} không có tracking, không cần đồng bộ trạng thái`);
            return;
        }

        // Kiểm tra trạng thái tracking để cập nhật trạng thái đơn hàng
        let orderStatus = "Chờ mua";
        const trackings = trackingsResult.rows;

        // Kiểm tra xem có tracking nào đã hoàn thành chưa
        const hasCompletedTracking = trackings.some(t => t.tracking_status === 'hoanthanh');
        const hasVNTracking = trackings.some(t => t.tracking_status === 'nhapkhovn');
        const hasShippingTracking = trackings.some(t => t.tracking_status === 'dangvevn');
        const hasWarehouseTracking = trackings.some(t => t.tracking_status === 'nhapkho');

        if (hasCompletedTracking) {
            orderStatus = "Đã hoàn thành";
        } else if (hasVNTracking) {
            orderStatus = "Đã nhập kho VN";
        } else if (hasShippingTracking) {
            orderStatus = "Đang vận chuyển về VN";
        } else if (hasWarehouseTracking) {
            orderStatus = "Đã nhập kho";
        } else {
            orderStatus = "Chờ nhận hàng";
        }

        console.log(`Đồng bộ trạng thái đơn hàng ${purchaseOrderId}: ${orderStatus}`);

        // Cập nhật trạng thái đơn hàng
        await pool.query(`
            UPDATE purchase_orders
            SET note = CASE
                WHEN note IS NULL OR note = '' THEN $2
                WHEN note NOT LIKE '%[Trạng thái:%' THEN note || ' | ' || $2
                ELSE regexp_replace(note, '\\[Trạng thái:.*?\\]', $2)
            END
            WHERE id = $1
        `, [purchaseOrderId, `[Trạng thái: ${orderStatus}]`]);

        return orderStatus;
    } catch (error) {
        console.error(`Lỗi khi đồng bộ trạng thái tracking cho đơn hàng ${purchaseOrderId}:`, error);
        throw error;
    }
}

/**
 * @api {get} /api/admin/purchase-orders Lấy danh sách đơn mua hộ
 * @apiName GetPurchaseOrders
 * @apiGroup PurchaseOrder
 * @apiPermission admin/staff
 *
 * @apiSuccess {Array} purchaseOrders Danh sách đơn mua hộ
 */
router.get('/', authenticateStaffToken, async (req, res) => {
    try {
        let purchaseOrders = [];

        // Nếu là admin, lấy tất cả đơn mua hộ (chỉ những đơn chưa bị xóa)
        if (req.admin) {
            const result = await pool.query(`
                SELECT po.*, u.username as user_username, s.fullname as staff_name, a.username as admin_username
                FROM purchase_orders po
                JOIN users u ON po.user_id = u.id
                LEFT JOIN staff s ON po.staff_id = s.id
                LEFT JOIN admin_users a ON po.created_by = a.id
                WHERE po.deleted_at IS NULL
                ORDER BY po.created_at DESC
            `);
            purchaseOrders = result.rows;
        } else {
            // Nếu là nhân viên, chỉ lấy đơn mua hộ do họ phụ trách (chỉ những đơn chưa bị xóa)
            const result = await pool.query(`
                SELECT po.*, u.username as user_username, s.fullname as staff_name, a.username as admin_username
                FROM purchase_orders po
                JOIN users u ON po.user_id = u.id
                LEFT JOIN staff s ON po.staff_id = s.id
                LEFT JOIN admin_users a ON po.created_by = a.id
                WHERE po.staff_id = $1 AND po.deleted_at IS NULL
                ORDER BY po.created_at DESC
            `, [req.staff.staffId]);
            purchaseOrders = result.rows;
        }

        // Nếu có đơn mua hộ, lấy thông tin sản phẩm cho mỗi đơn
        if (purchaseOrders.length > 0) {
            // Lấy tất cả ID của đơn mua hộ
            const orderIds = purchaseOrders.map(order => order.id);

            // Lấy thông tin sản phẩm cho tất cả đơn mua hộ (bao gồm giá mua và tỷ giá mua)
            const itemsResult = await pool.query(`
                SELECT purchase_order_id, product_name, product_variant, quantity, purchased_quantity, id,
                       price, purchase_price, exchange_rate, purchase_exchange_rate
                FROM purchase_order_items
                WHERE purchase_order_id = ANY($1)
                ORDER BY purchase_order_id, id
            `, [orderIds]);

            // Tạo map để lưu trữ sản phẩm theo ID đơn mua hộ
            const itemsByOrderId = {};
            itemsResult.rows.forEach(item => {
                if (!itemsByOrderId[item.purchase_order_id]) {
                    itemsByOrderId[item.purchase_order_id] = [];
                }
                itemsByOrderId[item.purchase_order_id].push(item);
            });

            // Lấy thông tin tracking cho tất cả đơn mua hộ
            const trackingsResult = await pool.query(`
                SELECT pot.*, poi.purchase_order_id, t.status as tracking_status
                FROM purchase_order_tracking pot
                JOIN purchase_order_items poi ON pot.purchase_order_item_id = poi.id
                LEFT JOIN tracking t ON pot.tracking_number = t.number
                WHERE poi.purchase_order_id = ANY($1)
                ORDER BY poi.purchase_order_id, pot.created_at DESC
            `, [orderIds]);

            // Tạo map để lưu trữ tracking theo ID đơn mua hộ
            const trackingsByOrderId = {};
            trackingsResult.rows.forEach(tracking => {
                if (!trackingsByOrderId[tracking.purchase_order_id]) {
                    trackingsByOrderId[tracking.purchase_order_id] = [];
                }
                trackingsByOrderId[tracking.purchase_order_id].push(tracking);
            });

            // Thêm danh sách sản phẩm và tracking vào mỗi đơn mua hộ
            purchaseOrders = purchaseOrders.map(order => {
                const orderTrackings = trackingsByOrderId[order.id] || [];
                const orderItems = itemsByOrderId[order.id] || [];

                // Tạo danh sách tracking numbers để hiển thị
                const trackingNumbers = orderTrackings.map(t => t.tracking_number);

                // Kiểm tra trạng thái tracking để cập nhật trạng thái đơn hàng
                let orderStatus = "Chờ mua";

                if (orderTrackings.length > 0) {
                    // Kiểm tra xem có tracking nào đã hoàn thành chưa
                    const hasCompletedTracking = orderTrackings.some(t => t.tracking_status === 'hoanthanh');
                    const hasVNTracking = orderTrackings.some(t => t.tracking_status === 'nhapkhovn');
                    const hasShippingTracking = orderTrackings.some(t => t.tracking_status === 'dangvevn');
                    const hasWarehouseTracking = orderTrackings.some(t => t.tracking_status === 'nhapkho');

                    if (hasCompletedTracking) {
                        orderStatus = "Đã hoàn thành";
                    } else if (hasVNTracking) {
                        orderStatus = "Đã nhập kho VN";
                    } else if (hasShippingTracking) {
                        orderStatus = "Đang vận chuyển về VN";
                    } else if (hasWarehouseTracking) {
                        orderStatus = "Đã nhập kho";
                    } else {
                        orderStatus = "Chờ nhận hàng";
                    }
                }

                // Tính toán thông tin tài chính
                let totalPurchasePrice = 0;
                let totalPurchaseAmount = 0;
                let totalSellAmount = 0;
                let totalGrossProfit = 0;
                let avgPurchaseExchangeRate = 0;
                let purchaseExchangeRateCount = 0;

                // Tính tổng giá mua và tỷ giá mua trung bình
                orderItems.forEach(item => {
                    const purchasedQty = item.purchased_quantity ? parseFloat(item.purchased_quantity) : 0;
                    const purchasePrice = item.purchase_price ? parseFloat(item.purchase_price) : 0;
                    const purchaseExchangeRate = item.purchase_exchange_rate ? parseFloat(item.purchase_exchange_rate) : 0;
                    const sellPrice = item.price ? parseFloat(item.price) : 0;
                    const sellQty = item.quantity ? parseFloat(item.quantity) : 0;

                    if (purchasePrice > 0 && purchasedQty > 0) {
                        totalPurchasePrice += purchasePrice * purchasedQty;

                        if (purchaseExchangeRate > 0) {
                            totalPurchaseAmount += purchasePrice * purchasedQty * purchaseExchangeRate;
                            avgPurchaseExchangeRate += purchaseExchangeRate;
                            purchaseExchangeRateCount++;
                        }
                    }

                    if (sellPrice > 0 && sellQty > 0) {
                        totalSellAmount += sellPrice * sellQty * parseFloat(order.exchange_rate);
                    }
                });

                // Tính tỷ giá mua trung bình
                avgPurchaseExchangeRate = purchaseExchangeRateCount > 0 ? avgPurchaseExchangeRate / purchaseExchangeRateCount : 0;

                // Tính lãi Gross
                totalGrossProfit = totalSellAmount - totalPurchaseAmount;

                // Tính tổng số tiền đơn hàng (VND)
                const totalOrderAmount = parseFloat(order.total_amount) * parseFloat(order.exchange_rate);

                return {
                    ...order,
                    items: orderItems,
                    trackings: orderTrackings,
                    tracking_numbers: trackingNumbers.length > 0 ? trackingNumbers : null,
                    order_status: orderStatus,
                    total_purchase_price: totalPurchasePrice > 0 ? totalPurchasePrice.toFixed(2) : null,
                    avg_purchase_exchange_rate: avgPurchaseExchangeRate > 0 ? avgPurchaseExchangeRate.toFixed(2) : null,
                    total_purchase_amount: totalPurchaseAmount > 0 ? totalPurchaseAmount.toFixed(2) : null,
                    total_sell_amount: totalSellAmount > 0 ? totalSellAmount.toFixed(2) : null,
                    gross_profit: totalGrossProfit !== 0 ? totalGrossProfit.toFixed(2) : null,
                    total_order_amount: totalOrderAmount > 0 ? totalOrderAmount.toFixed(2) : null
                };
            });
        }

        res.json(purchaseOrders);
    } catch (error) {
        console.error('Lỗi khi lấy danh sách đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy danh sách đơn mua hộ' });
    }
});

/**
 * @api {get} /api/admin/purchase-orders/:id Lấy chi tiết đơn mua hộ
 * @apiName GetPurchaseOrderDetail
 * @apiGroup PurchaseOrder
 * @apiPermission admin/staff
 *
 * @apiParam {Number} id ID của đơn mua hộ
 *
 * @apiSuccess {Object} purchaseOrder Thông tin đơn mua hộ
 * @apiSuccess {Array} items Danh sách các mục trong đơn mua hộ
 * @apiSuccess {Array} trackings Danh sách tracking của đơn mua hộ
 * @apiSuccess {Array} payments Danh sách thanh toán của đơn mua hộ
 */
router.get('/:id', authenticateStaffToken, async (req, res) => {
    try {
        const purchaseOrderId = req.params.id;

        // Lấy thông tin đơn mua hộ (chỉ những đơn chưa bị xóa)
        const purchaseOrderResult = await pool.query(`
            SELECT po.*, u.username as user_username, s.fullname as staff_name, a.username as admin_username
            FROM purchase_orders po
            JOIN users u ON po.user_id = u.id
            LEFT JOIN staff s ON po.staff_id = s.id
            LEFT JOIN admin_users a ON po.created_by = a.id
            WHERE po.id = $1 AND po.deleted_at IS NULL
        `, [purchaseOrderId]);

        if (purchaseOrderResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ' });
        }

        const purchaseOrder = purchaseOrderResult.rows[0];

        // Kiểm tra quyền truy cập (admin hoặc nhân viên phụ trách)
        if (!req.admin && purchaseOrder.staff_id !== req.staff.staffId) {
            return res.status(403).json({ message: 'Bạn không có quyền xem đơn mua hộ này' });
        }

        // Lấy danh sách các mục trong đơn mua hộ
        const itemsResult = await pool.query(`
            SELECT *
            FROM purchase_order_items
            WHERE purchase_order_id = $1
            ORDER BY id
        `, [purchaseOrderId]);

        // Lấy danh sách tracking của đơn mua hộ
        const trackingsResult = await pool.query(`
            SELECT pot.*, poi.id as purchase_order_item_id, w.name as warehouse_name, t.status as tracking_status
            FROM purchase_order_tracking pot
            JOIN purchase_order_items poi ON pot.purchase_order_item_id = poi.id
            LEFT JOIN warehouses w ON pot.warehouse_id = w.id
            LEFT JOIN tracking t ON pot.tracking_number = t.number
            WHERE poi.purchase_order_id = $1
            ORDER BY pot.created_at DESC
        `, [purchaseOrderId]);

        // Lấy lịch sử thanh toán
        const paymentsResult = await pool.query(`
            SELECT pop.*, a.username as admin_username
            FROM purchase_order_payments pop
            LEFT JOIN admin_users a ON pop.verified_by = a.id
            WHERE pop.purchase_order_id = $1
            ORDER BY pop.payment_date DESC
        `, [purchaseOrderId]);

        // Tính toán lại các giá trị tài chính
        const totalAmount = purchaseOrder.total_amount * purchaseOrder.exchange_rate;
        const paidAmount = purchaseOrder.paid_amount || 0;
        const calculatedRemainingAmount = Math.max(0, totalAmount - paidAmount);
        const calculatedDepositAmount = (totalAmount * purchaseOrder.deposit_percentage) / 100;

        // Cập nhật remaining_amount nếu khác với giá trị tính toán
        if (Math.abs(purchaseOrder.remaining_amount - calculatedRemainingAmount) > 0.01) {
            console.log(`Cập nhật remaining_amount cho đơn ${purchaseOrder.order_code}: ${purchaseOrder.remaining_amount} -> ${calculatedRemainingAmount}`);
            purchaseOrder.remaining_amount = calculatedRemainingAmount;
        }

        // Cập nhật deposit_amount nếu khác với giá trị tính toán
        if (Math.abs(purchaseOrder.deposit_amount - calculatedDepositAmount) > 0.01) {
            console.log(`Cập nhật deposit_amount cho đơn ${purchaseOrder.order_code}: ${purchaseOrder.deposit_amount} -> ${calculatedDepositAmount}`);
            purchaseOrder.deposit_amount = calculatedDepositAmount;
        }

        res.json({
            purchaseOrder: purchaseOrder,
            items: itemsResult.rows,
            trackings: trackingsResult.rows,
            payments: paymentsResult.rows
        });
    } catch (error) {
        console.error('Lỗi khi lấy chi tiết đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy chi tiết đơn mua hộ' });
    }
});

/**
 * @api {post} /api/admin/purchase-orders Tạo đơn mua hộ mới
 * @apiName CreatePurchaseOrder
 * @apiGroup PurchaseOrder
 * @apiPermission admin/staff
 *
 * @apiParam {Number} user_id ID của khách hàng
 * @apiParam {Array} items Danh sách các mục trong đơn mua hộ
 *
 * @apiSuccess {Object} purchaseOrder Thông tin đơn mua hộ đã tạo
 */
router.post('/', authenticateStaffToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const { user_id, items, exchange_rate, deposit_percentage, note } = req.body;

        // Kiểm tra thông tin bắt buộc
        if (!user_id || !items || !Array.isArray(items) || items.length === 0 || !exchange_rate || !deposit_percentage) {
            return res.status(400).json({ message: 'Thiếu thông tin bắt buộc' });
        }

        // Lấy thông tin user
        const userResult = await client.query('SELECT username, staff_id FROM users WHERE id = $1', [user_id]);
        if (userResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy user' });
        }

        const user = userResult.rows[0];
        const username = user.username;

        // Xác định staff_id (nhân viên phụ trách)
        // Nếu người tạo là nhân viên, gán họ là người phụ trách
        // Nếu người tạo là admin và có chỉ định staff_id, sử dụng staff_id đó
        // Nếu không, sử dụng staff_id của user (nếu có)
        let staffId = null;
        if (req.staff) {
            // Nếu người tạo là nhân viên, luôn gán họ là người phụ trách
            staffId = req.staff.staffId;
        } else if (req.body.staff_id) {
            // Nếu admin chỉ định nhân viên phụ trách
            staffId = req.body.staff_id;
        } else {
            // Sử dụng staff_id của user nếu có
            staffId = user.staff_id;
        }

        // Xác định created_by (chỉ có thể là admin_id)
        let adminId = null;
        if (req.admin) {
            adminId = req.admin.id;
        } else {
            // Nếu là nhân viên tạo đơn, cần lấy một admin_id mặc định
            const adminResult = await client.query('SELECT id FROM admin_users ORDER BY id LIMIT 1');
            if (adminResult.rows.length > 0) {
                adminId = adminResult.rows[0].id;
            } else {
                await client.query('ROLLBACK');
                return res.status(500).json({ message: 'Không thể tạo đơn mua hộ: Không tìm thấy admin' });
            }
        }

        // Tạo mã đơn mua hộ theo format CO + USERNAME (viết hoa) + SỐ THỨ TỰ (4 chữ số có số 0 đứng trước)
        // Tìm số thứ tự cao nhất của khách hàng này để tránh duplicate (chỉ đếm các đơn chưa bị xóa)
        const maxNumberResult = await client.query(`
            SELECT COALESCE(MAX(
                CASE
                    WHEN order_code ~ '^CO${username.toUpperCase()}[0-9]{4}$'
                    THEN CAST(SUBSTRING(order_code FROM ${4 + username.length}) AS INTEGER)
                    ELSE 0
                END
            ), 0) as max_number
            FROM purchase_orders
            WHERE order_code LIKE $1 AND deleted_at IS NULL
        `, [`CO${username.toUpperCase()}%`]);

        let nextNumber = parseInt(maxNumberResult.rows[0].max_number) + 1;
        let orderCode = `CO${username.toUpperCase()}${nextNumber.toString().padStart(4, '0')}`;

        // Retry mechanism để đảm bảo không bị duplicate trong trường hợp race condition
        let retryCount = 0;
        const maxRetries = 10;

        while (retryCount < maxRetries) {
            try {
                // Kiểm tra xem mã đã tồn tại chưa (chỉ kiểm tra các đơn chưa bị xóa)
                const existingResult = await client.query(
                    'SELECT id FROM purchase_orders WHERE order_code = $1 AND deleted_at IS NULL',
                    [orderCode]
                );

                if (existingResult.rows.length === 0) {
                    // Mã chưa tồn tại, có thể sử dụng
                    break;
                } else {
                    // Mã đã tồn tại, thử số tiếp theo
                    nextNumber++;
                    orderCode = `CO${username.toUpperCase()}${nextNumber.toString().padStart(4, '0')}`;
                    retryCount++;
                }
            } catch (error) {
                console.error(`Lỗi khi kiểm tra order_code (retry ${retryCount}):`, error);
                retryCount++;
                if (retryCount >= maxRetries) {
                    throw new Error('Không thể tạo mã đơn hàng unique sau nhiều lần thử');
                }
            }
        }

        if (retryCount >= maxRetries) {
            throw new Error('Không thể tạo mã đơn hàng unique sau nhiều lần thử');
        }

        // Tính tổng tiền và tiền cọc
        let totalAmount = 0;
        for (const item of items) {
            totalAmount += item.price * item.quantity;
        }

        // Tính toán số tiền cọc và số tiền còn lại
        const depositAmount = parseFloat(((totalAmount * exchange_rate) * (deposit_percentage / 100)).toFixed(2));
        const remainingAmount = parseFloat(((totalAmount * exchange_rate) - depositAmount).toFixed(2));

        console.log(`Tạo đơn hàng - Chi tiết tính toán:`);
        console.log(`- Tổng tiền: ${totalAmount} * ${exchange_rate} = ${totalAmount * exchange_rate}`);
        console.log(`- Tỷ lệ cọc: ${deposit_percentage}%`);
        console.log(`- Số tiền cọc: ${depositAmount}`);
        console.log(`- Số tiền còn lại: ${remainingAmount}`);

        // Tạo đơn mua hộ mới với additional error handling
        let purchaseOrderResult;
        try {
            purchaseOrderResult = await client.query(`
                INSERT INTO purchase_orders (
                    order_code, user_id, staff_id, total_amount, exchange_rate,
                    deposit_percentage, deposit_amount, remaining_amount,
                    payment_status, note, created_by
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                RETURNING *
            `, [
                orderCode, user_id, staffId, totalAmount, exchange_rate,
                deposit_percentage, depositAmount, remainingAmount,
                'pending', note || null, adminId
            ]);
        } catch (insertError) {
            if (insertError.code === '23505' && insertError.constraint === 'purchase_orders_order_code_key') {
                // Nếu vẫn bị duplicate sau retry mechanism, thử một lần nữa với timestamp
                const timestamp = Date.now().toString().slice(-4);
                const fallbackOrderCode = `CO${username.toUpperCase()}${timestamp}`;

                console.log(`Fallback to timestamp-based order code: ${fallbackOrderCode}`);
                console.log(`Rolling back and starting new transaction for fallback order code`);

                // ROLLBACK transaction hiện tại vì đã bị abort
                await client.query('ROLLBACK');
                
                // BEGIN transaction mới
                await client.query('BEGIN');

                purchaseOrderResult = await client.query(`
                    INSERT INTO purchase_orders (
                        order_code, user_id, staff_id, total_amount, exchange_rate,
                        deposit_percentage, deposit_amount, remaining_amount,
                        payment_status, note, created_by
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                    RETURNING *
                `, [
                    fallbackOrderCode, user_id, staffId, totalAmount, exchange_rate,
                    deposit_percentage, depositAmount, remainingAmount,
                    'pending', note || null, adminId
                ]);
            } else {
                throw insertError;
            }
        }

        const purchaseOrderId = purchaseOrderResult.rows[0].id;

        // Thêm các mục vào đơn mua hộ
        for (const item of items) {
            await client.query(`
                INSERT INTO purchase_order_items (
                    purchase_order_id, product_link, product_name, product_variant,
                    quantity, price, exchange_rate
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            `, [
                purchaseOrderId, item.product_link || null, item.product_name,
                item.product_variant || null, item.quantity, item.price, exchange_rate
            ]);
        }

        await client.query('COMMIT');

        res.status(201).json({
            message: 'Đã tạo đơn mua hộ thành công',
            purchaseOrder: purchaseOrderResult.rows[0]
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('Lỗi khi tạo đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi tạo đơn mua hộ' });
    } finally {
        client.release();
    }
});

/**
 * @api {post} /api/admin/purchase-orders/:id/tracking Thêm tracking cho đơn mua hộ
 * @apiName AddPurchaseOrderTracking
 * @apiGroup PurchaseOrder
 * @apiPermission admin
 *
 * @apiParam {Number} id ID của đơn mua hộ
 * @apiParam {Number} purchase_order_item_id ID của mục trong đơn mua hộ
 * @apiParam {String} tracking_number Số tracking
 * @apiParam {Number} warehouse_id ID của kho
 *
 * @apiSuccess {Object} tracking Thông tin tracking đã thêm
 */
router.post('/:id/tracking', authenticateAdminToken, async (req, res) => {
    try {
        const purchaseOrderId = req.params.id;
        const { purchase_order_item_id, tracking_number, warehouse_id } = req.body;

        // Kiểm tra thông tin bắt buộc
        if (!purchase_order_item_id || !tracking_number || !warehouse_id) {
            return res.status(400).json({ message: 'Thiếu thông tin bắt buộc' });
        }

        // Kiểm tra đơn mua hộ có tồn tại không
        const purchaseOrderResult = await pool.query(
            'SELECT * FROM purchase_orders WHERE id = $1',
            [purchaseOrderId]
        );

        if (purchaseOrderResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ' });
        }

        // Kiểm tra mục trong đơn mua hộ có tồn tại không
        const itemResult = await pool.query(
            'SELECT * FROM purchase_order_items WHERE id = $1 AND purchase_order_id = $2',
            [purchase_order_item_id, purchaseOrderId]
        );

        if (itemResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy mục trong đơn mua hộ' });
        }

        // Kiểm tra kho có tồn tại không
        const warehouseResult = await pool.query(
            'SELECT * FROM warehouses WHERE id = $1',
            [warehouse_id]
        );

        if (warehouseResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy kho' });
        }

        // Kiểm tra xem tracking đã tồn tại trong bảng tracking chưa
        const existingTrackingResult = await pool.query(
            'SELECT * FROM tracking WHERE number = $1',
            [tracking_number]
        );

        let trackingStatus = 'chonhanhang';

        // Nếu tracking đã tồn tại, sử dụng status hiện tại của nó
        if (existingTrackingResult.rows.length > 0) {
            trackingStatus = existingTrackingResult.rows[0].status;
            console.log(`Tracking ${tracking_number} đã tồn tại với trạng thái: ${trackingStatus}`);
        }

        // Thêm tracking mới vào purchase_order_tracking
        const trackingResult = await pool.query(`
            INSERT INTO purchase_order_tracking (
                purchase_order_item_id, tracking_number, warehouse_id, status
            )
            VALUES ($1, $2, $3, $4)
            RETURNING *
        `, [
            purchase_order_item_id, tracking_number, warehouse_id, trackingStatus
        ]);

        // Nếu tracking chưa tồn tại, thêm vào bảng tracking chính
        const purchaseOrder = purchaseOrderResult.rows[0];
        if (existingTrackingResult.rows.length === 0) {
            await pool.query(`
                INSERT INTO tracking (
                    number, status, user_id, warehouse_id
                )
                VALUES ($1, $2, $3, $4)
            `, [
                tracking_number, trackingStatus, purchaseOrder.user_id, warehouse_id
            ]);
            console.log(`Đã thêm tracking ${tracking_number} mới vào bảng tracking chính`);
        } else {
            console.log(`Sử dụng tracking ${tracking_number} đã tồn tại`);
        }

        // Đồng bộ trạng thái đơn hàng sau khi thêm tracking
        const orderStatus = await syncPurchaseOrderTrackingStatus(purchaseOrderId);

        res.status(201).json({
            message: 'Đã thêm tracking thành công',
            tracking: {
                ...trackingResult.rows[0],
                tracking_status: trackingStatus
            },
            order_status: orderStatus
        });
    } catch (error) {
        console.error('Lỗi khi thêm tracking cho đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi thêm tracking cho đơn mua hộ' });
    }
});

/**
 * @api {put} /api/admin/purchase-orders/:id Cập nhật toàn bộ thông tin đơn mua hộ
 * @apiName UpdatePurchaseOrder
 * @apiGroup PurchaseOrder
 * @apiPermission admin
 *
 * @apiParam {Number} id ID của đơn mua hộ
 * @apiParam {Number} user_id ID của khách hàng
 * @apiParam {Number} staff_id ID của nhân viên phụ trách
 * @apiParam {Number} exchange_rate Tỷ giá bán
 * @apiParam {Number} deposit_percentage Phần trăm cọc
 * @apiParam {String} note Ghi chú
 * @apiParam {Array} items Danh sách sản phẩm
 *
 * @apiSuccess {Object} purchaseOrder Thông tin đơn mua hộ đã cập nhật
 */
router.put('/:id', authenticateAdminToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const purchaseOrderId = req.params.id;
        const { user_id, staff_id, exchange_rate, deposit_percentage, note, items } = req.body;

        // Kiểm tra thông tin bắt buộc
        if (!user_id || !exchange_rate || !deposit_percentage || !items || !Array.isArray(items) || items.length === 0) {
            return res.status(400).json({ message: 'Thiếu thông tin bắt buộc' });
        }

        // Kiểm tra đơn mua hộ có tồn tại không
        const purchaseOrderResult = await client.query(
            'SELECT * FROM purchase_orders WHERE id = $1',
            [purchaseOrderId]
        );

        if (purchaseOrderResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ' });
        }

        const currentOrder = purchaseOrderResult.rows[0];

        // Kiểm tra khách hàng có tồn tại không
        const userResult = await client.query(
            'SELECT id FROM users WHERE id = $1',
            [user_id]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy khách hàng' });
        }

        // Kiểm tra nhân viên có tồn tại không (nếu có staff_id)
        if (staff_id) {
            const staffResult = await client.query(
                'SELECT id FROM staff WHERE id = $1',
                [staff_id]
            );

            if (staffResult.rows.length === 0) {
                return res.status(404).json({ message: 'Không tìm thấy nhân viên' });
            }
        }

        // Tính tổng tiền từ các sản phẩm
        let totalAmount = 0;
        for (const item of items) {
            if (!item.product_name || !item.quantity || !item.price) {
                return res.status(400).json({ message: 'Thông tin sản phẩm không đầy đủ' });
            }
            totalAmount += parseFloat(item.quantity) * parseFloat(item.price);
        }

        // Lấy tổng số tiền đã thanh toán từ database
        const paymentsResult = await client.query(
            'SELECT COALESCE(SUM(amount), 0) as total_paid FROM purchase_order_payments WHERE purchase_order_id = $1',
            [purchaseOrderId]
        );
        const totalPaidAmount = parseFloat(paymentsResult.rows[0].total_paid) || 0;

        // Tính tiền cọc và số tiền còn lại với logic cải tiến
        const depositAmount = parseFloat(((totalAmount * parseFloat(exchange_rate)) * (parseFloat(deposit_percentage) / 100)).toFixed(2));
        const totalOrderAmount = parseFloat((totalAmount * parseFloat(exchange_rate)).toFixed(2));
        
        // Tính số tiền còn lại với tolerance 1 VNĐ
        let remainingAmount = parseFloat((totalOrderAmount - totalPaidAmount).toFixed(2));
        
        // Xử lý tolerance: nếu còn lại <= 1 VNĐ, coi như đã thanh toán đủ
        const tolerance = 1.0;
        if (remainingAmount <= tolerance && remainingAmount > 0) {
            console.log(`Áp dụng tolerance: Số tiền còn lại ${remainingAmount} VNĐ <= ${tolerance} VNĐ, coi như đã thanh toán đủ`);
            remainingAmount = 0;
        } else if (remainingAmount < 0) {
            remainingAmount = 0; // Không cho phép số âm
        }

        // Tính toán trạng thái thanh toán mới
        let newPaymentStatus = currentOrder.payment_status; // Giữ nguyên trạng thái hiện tại
        const epsilon = 0.01; // Dung sai cho việc so sánh số thực

        if (remainingAmount <= epsilon) {
            // Đã thanh toán đủ toàn bộ số tiền
            newPaymentStatus = 'completed';
            console.log(`Trạng thái thanh toán mới: completed (đã thanh toán đủ)`);
        } else if (totalPaidAmount + epsilon >= depositAmount) {
            // Đã thanh toán đủ tiền cọc nhưng chưa thanh toán đủ toàn bộ
            newPaymentStatus = 'deposited';
            console.log(`Trạng thái thanh toán mới: deposited (đã cọc)`);
        } else if (totalPaidAmount > epsilon) {
            // Đã có thanh toán nhưng chưa đủ tiền cọc
            newPaymentStatus = 'pending';
            console.log(`Trạng thái thanh toán mới: pending (đã thanh toán một phần)`);
        } else {
            // Chưa có thanh toán nào
            newPaymentStatus = 'pending';
            console.log(`Trạng thái thanh toán mới: pending (chưa thanh toán)`);
        }

        console.log(`Cập nhật đơn hàng ${purchaseOrderId}:`);
        console.log(`- Tổng tiền cũ: ${currentOrder.total_amount * currentOrder.exchange_rate} VNĐ`);
        console.log(`- Tổng tiền mới: ${totalOrderAmount} VNĐ`);
        console.log(`- Đã thanh toán: ${totalPaidAmount} VNĐ`);
        console.log(`- Tiền cọc: ${depositAmount} VNĐ`);
        console.log(`- Còn lại: ${remainingAmount} VNĐ`);
        console.log(`- Trạng thái cũ: ${currentOrder.payment_status}`);
        console.log(`- Trạng thái mới: ${newPaymentStatus}`);

        // Cập nhật thông tin đơn mua hộ với xử lý lỗi cho database chưa cập nhật
        let updateOrderResult;
        try {
            updateOrderResult = await client.query(`
                UPDATE purchase_orders
                SET user_id = $1, staff_id = $2, total_amount = $3, exchange_rate = $4,
                    deposit_percentage = $5, deposit_amount = $6, remaining_amount = $7,
                    payment_status = $8, paid_amount = $9, note = $10, updated_at = CURRENT_TIMESTAMP
                WHERE id = $11
                RETURNING *
            `, [
                user_id, staff_id, totalAmount, exchange_rate,
                deposit_percentage, depositAmount, remainingAmount,
                newPaymentStatus, totalPaidAmount, note || null, purchaseOrderId
            ]);
        } catch (updateError) {
            console.log('Lỗi khi cập nhật với trạng thái mới, thử fallback...', updateError.message);
            
            // Fallback 1: Thử không có cột paid_amount
            if (updateError.code === '42703' && updateError.message.includes('paid_amount')) {
                console.log('Cột paid_amount chưa tồn tại, cập nhật không có cột này');
                updateOrderResult = await client.query(`
                    UPDATE purchase_orders
                    SET user_id = $1, staff_id = $2, total_amount = $3, exchange_rate = $4,
                        deposit_percentage = $5, deposit_amount = $6, remaining_amount = $7,
                        payment_status = $8, note = $9, updated_at = CURRENT_TIMESTAMP
                    WHERE id = $10
                    RETURNING *
                `, [
                    user_id, staff_id, totalAmount, exchange_rate,
                    deposit_percentage, depositAmount, remainingAmount,
                    newPaymentStatus, note || null, purchaseOrderId
                ]);
            }
            // Fallback 2: Thử với trạng thái 'pending' nếu 'deposited' không được hỗ trợ
            else if (updateError.code === '23514' && updateError.constraint === 'purchase_orders_payment_status_check') {
                console.log('Trạng thái "deposited" chưa được hỗ trợ, sử dụng "pending"');
                const fallbackStatus = newPaymentStatus === 'deposited' ? 'pending' : newPaymentStatus;
                
                try {
                    updateOrderResult = await client.query(`
                        UPDATE purchase_orders
                        SET user_id = $1, staff_id = $2, total_amount = $3, exchange_rate = $4,
                            deposit_percentage = $5, deposit_amount = $6, remaining_amount = $7,
                            payment_status = $8, paid_amount = $9, note = $10, updated_at = CURRENT_TIMESTAMP
                        WHERE id = $11
                        RETURNING *
                    `, [
                        user_id, staff_id, totalAmount, exchange_rate,
                        deposit_percentage, depositAmount, remainingAmount,
                        fallbackStatus, totalPaidAmount, note || null, purchaseOrderId
                    ]);
                } catch (secondError) {
                    if (secondError.code === '42703' && secondError.message.includes('paid_amount')) {
                        console.log('Cả cột paid_amount và trạng thái deposited đều chưa hỗ trợ');
                        updateOrderResult = await client.query(`
                            UPDATE purchase_orders
                            SET user_id = $1, staff_id = $2, total_amount = $3, exchange_rate = $4,
                                deposit_percentage = $5, deposit_amount = $6, remaining_amount = $7,
                                payment_status = $8, note = $9, updated_at = CURRENT_TIMESTAMP
                            WHERE id = $10
                            RETURNING *
                        `, [
                            user_id, staff_id, totalAmount, exchange_rate,
                            deposit_percentage, depositAmount, remainingAmount,
                            fallbackStatus, note || null, purchaseOrderId
                        ]);
                    } else {
                        throw secondError;
                    }
                }
                
                // Thêm ghi chú về trạng thái thực tế nếu phải dùng fallback
                if (newPaymentStatus === 'deposited' && fallbackStatus === 'pending') {
                    await client.query(`
                        UPDATE purchase_orders
                        SET note = CASE
                            WHEN note IS NULL OR note = '' THEN '[ĐÃ CỌC] Đã thanh toán đủ tiền cọc'
                            WHEN note NOT LIKE '%[ĐÃ CỌC]%' THEN note || ' | [ĐÃ CỌC] Đã thanh toán đủ tiền cọc'
                            ELSE note
                        END
                        WHERE id = $1
                    `, [purchaseOrderId]);
                    console.log('Đã thêm ghi chú "[ĐÃ CỌC]" vào đơn hàng');
                }
            } else {
                throw updateError;
            }
        }

        // Lấy danh sách items hiện có
        const existingItemsResult = await client.query(
            'SELECT * FROM purchase_order_items WHERE purchase_order_id = $1',
            [purchaseOrderId]
        );
        const existingItems = existingItemsResult.rows;
        const existingItemIds = existingItems.map(item => item.id);

        // Tạo danh sách ID của items trong request (chỉ những items có ID)
        const requestItemIds = items.filter(item => item.id).map(item => parseInt(item.id));

        console.log(`Xử lý cập nhật items cho đơn hàng ${purchaseOrderId}:`);
        console.log(`- Items hiện có: ${existingItemIds.length} items (IDs: ${existingItemIds.join(', ')})`);
        console.log(`- Items trong request: ${items.length} items`);
        console.log(`- Items có ID trong request: ${requestItemIds.length} items (IDs: ${requestItemIds.join(', ')})`);

        // Xử lý từng item trong request
        for (const item of items) {
            if (item.id && parseInt(item.id) > 0) {
                // Item có ID - cập nhật item hiện có
                const itemId = parseInt(item.id);
                console.log(`Cập nhật item ID ${itemId}: ${item.product_name}`);

                await client.query(`
                    UPDATE purchase_order_items
                    SET product_link = $1, product_name = $2, product_variant = $3,
                        quantity = $4, price = $5, exchange_rate = $6,
                        purchased_quantity = $7, purchase_price = $8, purchase_exchange_rate = $9,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = $10 AND purchase_order_id = $11
                `, [
                    item.product_link || null, item.product_name, item.product_variant || null,
                    item.quantity, item.price, exchange_rate,
                    item.purchased_quantity || 0, item.purchase_price || 0, item.purchase_exchange_rate || 0,
                    itemId, purchaseOrderId
                ]);
            } else {
                // Item không có ID - thêm item mới
                console.log(`Thêm item mới: ${item.product_name}`);

                await client.query(`
                    INSERT INTO purchase_order_items (
                        purchase_order_id, product_link, product_name, product_variant,
                        quantity, price, exchange_rate, purchased_quantity, purchase_price, purchase_exchange_rate
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                `, [
                    purchaseOrderId, item.product_link || null, item.product_name,
                    item.product_variant || null, item.quantity, item.price, exchange_rate,
                    item.purchased_quantity || 0, item.purchase_price || 0, item.purchase_exchange_rate || 0
                ]);
            }
        }

        // Tìm các items cũ cần xóa (items hiện có nhưng không có trong request)
        const itemsToDelete = existingItemIds.filter(id => !requestItemIds.includes(id));

        if (itemsToDelete.length > 0) {
            console.log(`Xóa ${itemsToDelete.length} items không còn cần thiết (IDs: ${itemsToDelete.join(', ')})`);

            // Kiểm tra xem các items này có tracking không
            for (const itemId of itemsToDelete) {
                const trackingResult = await client.query(
                    'SELECT COUNT(*) as tracking_count FROM purchase_order_tracking WHERE purchase_order_item_id = $1',
                    [itemId]
                );

                const trackingCount = parseInt(trackingResult.rows[0].tracking_count);
                if (trackingCount > 0) {
                    console.log(`CẢNH BÁO: Item ID ${itemId} có ${trackingCount} tracking numbers. Tracking sẽ bị xóa theo.`);
                }
            }

            // Xóa các items không còn cần thiết (tracking sẽ bị xóa theo do ON DELETE CASCADE)
            await client.query(
                'DELETE FROM purchase_order_items WHERE id = ANY($1) AND purchase_order_id = $2',
                [itemsToDelete, purchaseOrderId]
            );
        } else {
            console.log('Không có items nào cần xóa');
        }

        await client.query('COMMIT');

        res.json({
            message: 'Đã cập nhật đơn mua hộ thành công',
            purchaseOrder: updateOrderResult.rows[0]
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('Lỗi khi cập nhật đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi cập nhật đơn mua hộ' });
    } finally {
        client.release();
    }
});

/**
 * @api {put} /api/admin/purchase-orders/:id/items/:itemId Cập nhật thông tin mục trong đơn mua hộ
 * @apiName UpdatePurchaseOrderItem
 * @apiGroup PurchaseOrder
 * @apiPermission admin
 *
 * @apiParam {Number} id ID của đơn mua hộ
 * @apiParam {Number} itemId ID của mục trong đơn mua hộ
 * @apiParam {Number} purchased_quantity Số lượng đã mua
 * @apiParam {Number} purchase_price Giá mua
 * @apiParam {Number} purchase_exchange_rate Tỷ giá mua
 *
 * @apiSuccess {Object} item Thông tin mục đã cập nhật
 */
router.put('/:id/items/:itemId', authenticateAdminToken, async (req, res) => {
    try {
        const purchaseOrderId = req.params.id;
        const itemId = req.params.itemId;
        const { purchased_quantity, purchase_price, purchase_exchange_rate } = req.body;

        // Kiểm tra đơn mua hộ có tồn tại không
        const purchaseOrderResult = await pool.query(
            'SELECT * FROM purchase_orders WHERE id = $1',
            [purchaseOrderId]
        );

        if (purchaseOrderResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ' });
        }

        // Kiểm tra mục trong đơn mua hộ có tồn tại không
        const itemResult = await pool.query(
            'SELECT * FROM purchase_order_items WHERE id = $1 AND purchase_order_id = $2',
            [itemId, purchaseOrderId]
        );

        if (itemResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy mục trong đơn mua hộ' });
        }

        // Cập nhật thông tin mục
        const updateResult = await pool.query(`
            UPDATE purchase_order_items
            SET purchased_quantity = $1, purchase_price = $2, purchase_exchange_rate = $3
            WHERE id = $4
            RETURNING *
        `, [
            purchased_quantity || 0,
            purchase_price || 0,
            purchase_exchange_rate || 0,
            itemId
        ]);

        res.json({
            message: 'Đã cập nhật thông tin mục thành công',
            item: updateResult.rows[0]
        });
    } catch (error) {
        console.error('Lỗi khi cập nhật thông tin mục trong đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi cập nhật thông tin mục trong đơn mua hộ' });
    }
});

/**
 * @api {post} /api/admin/purchase-orders/:id/check-payment Kiểm tra thanh toán đơn mua hộ
 * @apiName CheckPurchaseOrderPayment
 * @apiGroup PurchaseOrder
 * @apiPermission admin
 *
 * @apiParam {Number} id ID của đơn mua hộ
 *
 * @apiSuccess {Object} result Kết quả kiểm tra thanh toán
 */
router.post('/:id/check-payment', authenticateAdminToken, async (req, res) => {
    try {
        const purchaseOrderId = req.params.id;

        // Kiểm tra đơn mua hộ có tồn tại không
        const purchaseOrderResult = await pool.query(
            'SELECT po.*, u.username FROM purchase_orders po JOIN users u ON po.user_id = u.id WHERE po.id = $1',
            [purchaseOrderId]
        );

        if (purchaseOrderResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ' });
        }

        const purchaseOrder = purchaseOrderResult.rows[0];

        // Kiểm tra nếu đơn hàng đã thanh toán đầy đủ
        if (purchaseOrder.payment_status === 'completed') {
            return res.json({
                message: 'Đơn mua hộ đã được thanh toán đầy đủ',
                status: 'completed',
                purchaseOrder
            });
        }

        // Hàm gọi API TPBank trực tiếp với xử lý token hết hạn và proxy
        const callTPBankAPI = async (retryCount = 0) => {
            const MAX_RETRIES = 2;
            const RETRY_DELAY = 2000; // 2 seconds delay between retries

            try {
                // Kiểm tra giới hạn retry
                if (retryCount > MAX_RETRIES) {
                    console.log(`[Retry ${retryCount}] Đã vượt quá giới hạn retry (${MAX_RETRIES}), dừng thử lại`);
                    throw new Error('Đã vượt quá số lần thử lại tối đa cho TPBank API');
                }

                // Kiểm tra token validity trước khi gọi API (chỉ log, không block)
                if (retryCount > 0) {
                    const tokenValid = tpbankAPI.accessToken && Date.now() < tpbankAPI.accessTokenExpiry;
                    console.log(`[Retry ${retryCount}] Token validity check: ${tokenValid ? 'VALID' : 'INVALID/EXPIRED'}`);
                    if (tpbankAPI.accessToken) {
                        const timeToExpiry = tpbankAPI.accessTokenExpiry - Date.now();
                        console.log(`[Retry ${retryCount}] Time to token expiry: ${timeToExpiry}ms`);
                    }
                }

                // Tăng khoảng thời gian tìm kiếm lên 30 ngày để kiểm tra nhiều giao dịch hơn
                const days = 30;
                console.log(`[Retry ${retryCount}] Kiểm tra thanh toán cho đơn mua hộ ${purchaseOrderId} (${purchaseOrder.order_code}) trong ${days} ngày gần đây`);

                // Thêm delay giữa các lần retry để tránh spam
                if (retryCount > 0) {
                    console.log(`[Retry ${retryCount}] Chờ ${RETRY_DELAY}ms trước khi thử lại...`);
                    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                }

                // Gọi trực tiếp TPBank API thông qua tpbankAPI.getHistories
                console.log(`[Retry ${retryCount}] Calling TPBank API directly via proxy`);

                // Lấy thông tin cần thiết
                const username = process.env.TPBANK_USERNAME;
                const password = process.env.PASSWORD;
                const deviceId = process.env.DEVICE_ID;
                const accountId = process.env.ACCOUNT_ID;

                if (!username || !password || !deviceId || !accountId) {
                    throw new Error('Thiếu thông tin cấu hình TPBank trong biến môi trường');
                }

                // Gọi trực tiếp getHistories với token hiện tại
                const tpbankData = await tpbankAPI.getHistories(
                    tpbankAPI.accessToken,
                    accountId,
                    deviceId,
                    username,
                    password,
                    days,           // customDays
                    null,           // customStartDate
                    null            // customEndDate
                );

                console.log(`[Retry ${retryCount}] TPBank API call completed successfully`);

                // Kiểm tra dữ liệu trả về
                if (!tpbankData || !tpbankData.transactionInfos) {
                    console.log(`[Retry ${retryCount}] Invalid TPBank response data:`, tpbankData);
                    throw new Error('Không thể lấy dữ liệu giao dịch từ TPBank');
                }

                console.log(`[Retry ${retryCount}] Đã lấy ${tpbankData.transactionInfos.length} giao dịch từ TPBank`);
                return { info: tpbankData }; // Wrap để tương thích với code hiện tại

            } catch (error) {
                console.error(`[Retry ${retryCount}] Lỗi trong callTPBankAPI:`, error.message);

                // Nếu đã vượt quá retry limit, throw error
                if (retryCount >= MAX_RETRIES) {
                    throw error;
                }

                // Nếu là lỗi proxy connection failed, propagate để client xử lý
                if (error.message === 'PROXY_CONNECTION_FAILED' && error.shouldFallbackToClient) {
                    throw error;
                }

                // Thử lại với retry count tăng lên
                console.log(`[Retry ${retryCount}] Thử lại lần ${retryCount + 1}...`);
                return await callTPBankAPI(retryCount + 1);
            }
        };



        // Gọi API TPBank với xử lý token hết hạn và proxy fallback
        let tpbankData;
        try {
            tpbankData = await callTPBankAPI();
        } catch (error) {
            console.error('Lỗi khi gọi API TPBank:', error.message);

            // Xử lý các loại lỗi khác nhau
            let errorMessage = 'Không thể kết nối với TPBank API';
            let statusCode = 500;
            let shouldFallbackToClient = false;
            let fallbackData = null;

            if (error.message.includes('PROXY_CONNECTION_FAILED')) {
                errorMessage = 'Lỗi kết nối proxy TPBank. Hệ thống sẽ thử fallback client-side.';
                statusCode = 502;
                shouldFallbackToClient = true;
                fallbackData = {
                    username: process.env.TPBANK_USERNAME,
                    password: process.env.PASSWORD,
                    deviceId: process.env.DEVICE_ID
                };
            } else if (error.message.includes('FORCE_CLIENT_CONNECTION')) {
                errorMessage = 'Hệ thống được cấu hình sử dụng kết nối client-side. Vui lòng đăng nhập TPBank từ tab "Sao kê TPBank" trước.';
                statusCode = 503;
                shouldFallbackToClient = true;
                fallbackData = {
                    username: process.env.TPBANK_USERNAME,
                    password: process.env.PASSWORD,
                    deviceId: process.env.DEVICE_ID
                };
            } else if (error.message.includes('Thiếu thông tin cấu hình TPBank')) {
                errorMessage = 'Thiếu thông tin cấu hình TPBank trong hệ thống. Vui lòng liên hệ admin.';
                statusCode = 500;
            } else if (error.message.includes('Token TPBank hết hạn')) {
                errorMessage = 'Token TPBank đã hết hạn và không thể làm mới tự động. Vui lòng đăng nhập lại từ tab "Sao kê TPBank".';
                statusCode = 401;
            }

            const response = {
                message: errorMessage,
                error: error.message,
                suggestion: statusCode === 401 || statusCode === 503 ? 'Vui lòng quay lại tab "Sao kê TPBank" để đăng nhập lại' : null
            };

            // Thêm fallback data nếu cần
            if (shouldFallbackToClient && fallbackData) {
                response.shouldFallbackToClient = true;
                response.fallbackData = fallbackData;
            }

            return res.status(statusCode).json(response);
        }

        // Lấy danh sách giao dịch từ response
        const transactions = tpbankData.info.transactionInfos;
        console.log(`Tổng số giao dịch cần kiểm tra: ${transactions.length}`);

        // Tìm giao dịch có mã đơn mua hộ trong nội dung
        // Lọc lại chỉ lấy các giao dịch nhận tiền dựa vào số tiền (số tiền > 0)
        const matchingTransactions = transactions.filter(transaction => {
            if (!transaction.description) return false;

            // Chuyển đổi số tiền thành số (loại bỏ dấu phẩy)
            const amount = parseFloat(transaction.amount.replace(/,/g, ''));

            // Giao dịch nhận tiền có số tiền > 0
            const isIncomingTransaction = amount > 0;

            if (!isIncomingTransaction) return false;

            // Cải thiện logic tìm kiếm mã đơn hàng với exact match để tránh false positive
            const description = transaction.description.toUpperCase();
            const orderCode = purchaseOrder.order_code.toUpperCase();

            // Tạo regex pattern để tìm kiếm exact match với word boundary
            // Pattern sẽ tìm mã đơn hàng được bao quanh bởi ký tự không phải chữ/số
            const escapeRegex = (str) => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const escapedOrderCode = escapeRegex(orderCode);

            // Tạo các pattern cho exact match:
            // 1. Mã đơn hàng với word boundary (bao quanh bởi ký tự không phải chữ/số)
            const exactPattern = new RegExp(`(?:^|[^A-Z0-9])${escapedOrderCode}(?:[^A-Z0-9]|$)`, 'i');

            // 2. Mã đơn hàng với các ký tự phân cách thông thường
            const separatorPattern = new RegExp(`(?:^|[\\s\\.\\-_])${escapedOrderCode}(?:[\\s\\.\\-_]|$)`, 'i');

            // 3. Kiểm tra trong nội dung đã làm sạch với exact match
            const cleanDescription = description.replace(/[^A-Z0-9]/g, '');
            const cleanOrderCode = orderCode.replace(/[^A-Z0-9]/g, '');

            // Tạo regex cho exact match trong clean string (word boundary cho alphanumeric)
            const cleanExactPattern = new RegExp(`(?:^|[^A-Z0-9])${escapeRegex(cleanOrderCode)}(?:[^A-Z0-9]|$)`);
            const cleanExactMatch = cleanExactPattern.test(cleanDescription) &&
                                  cleanOrderCode.length >= 8; // Đảm bảo mã đơn hàng đủ dài

            // 4. Kiểm tra trường hợp đặc biệt: mã đơn hàng có ký tự đặc biệt được normalize
            const normalizedOrderCode = orderCode.replace(/[\s\.\-_]/g, '');
            // Sử dụng simple includes cho normalized match vì trong clean string không có ký tự đặc biệt
            const normalizedMatch = cleanDescription.includes(normalizedOrderCode) &&
                                  normalizedOrderCode.length >= 8;

            const isMatch = exactPattern.test(description) ||
                          separatorPattern.test(description) ||
                          cleanExactMatch ||
                          normalizedMatch;

            if (isMatch) {
                console.log(`>>> Tìm thấy giao dịch khớp với mã đơn mua hộ ${purchaseOrder.order_code}:`);
                console.log(`- Số tiền: ${transaction.amount}`);
                console.log(`- Nội dung gốc: "${transaction.description}"`);
                console.log(`- Nội dung đã làm sạch: "${cleanDescription}"`);
                console.log(`- Mã đơn hàng tìm kiếm: "${cleanOrderCode}"`);
            }

            return isMatch;
        });

        if (matchingTransactions.length === 0) {
            return res.json({
                message: 'Không tìm thấy giao dịch thanh toán cho đơn mua hộ này',
                status: 'pending',
                purchaseOrder
            });
        }

        // Tính tổng số tiền đã thanh toán
        let totalPaid = 0;
        const newPayments = [];

        console.log(`Bắt đầu xử lý ${matchingTransactions.length} giao dịch khớp với mã đơn mua hộ ${purchaseOrder.order_code}`);

        for (const transaction of matchingTransactions) {
            // Tạo ID giao dịch thay thế nếu không có transactionId
            // Sử dụng hash để tạo ID ngắn gọn nhưng vẫn đảm bảo tính duy nhất
            let transactionId = transaction.transactionId;

            if (!transactionId) {
                // Tạo hash từ nội dung giao dịch để đảm bảo tính duy nhất
                const contentToHash = `${transaction.transactionDate}_${transaction.description}_${transaction.amount}`;
                const hash = crypto.createHash('md5').update(contentToHash).digest('hex').substring(0, 16);

                // Tạo transaction_id ngắn gọn: ngày_hash (tối đa ~35 ký tự)
                transactionId = `${transaction.transactionDate.replace(/[-\s:]/g, '')}_${hash}`;

                // Đảm bảo không vượt quá 95 ký tự (để có buffer)
                if (transactionId.length > 95) {
                    transactionId = transactionId.substring(0, 95);
                }
            }

            console.log(`Xử lý giao dịch với ID: ${transactionId} (độ dài: ${transactionId.length})`);

            // Kiểm tra xem giao dịch đã được ghi nhận chưa
            const existingPayment = await pool.query(
                'SELECT * FROM purchase_order_payments WHERE transaction_id = $1',
                [transactionId]
            );

            if (existingPayment.rows.length === 0) {
                // Nếu chưa ghi nhận, thêm vào danh sách thanh toán mới
                console.log(`Giao dịch mới chưa được ghi nhận: ${transactionId}`);

                // Xử lý số tiền, loại bỏ dấu phẩy và chuyển đổi sang số
                const amount = parseFloat(transaction.amount.replace(/,/g, ''));
                console.log(`Số tiền giao dịch: ${transaction.amount} => ${amount}`);
                totalPaid += amount;

                // Thêm vào bảng purchase_order_payments với xử lý lỗi
                try {
                    const paymentResult = await pool.query(`
                        INSERT INTO purchase_order_payments (
                            purchase_order_id, amount, payment_date, transaction_id, verified_by
                        )
                        VALUES ($1, $2, $3, $4, $5)
                        RETURNING *
                    `, [
                        purchaseOrderId, amount, new Date(transaction.transactionDate),
                        transactionId, req.admin.id
                    ]);

                    console.log(`Đã thêm giao dịch ${transactionId} vào bảng purchase_order_payments với ID ${paymentResult.rows[0].id}`);

                    newPayments.push({
                        ...paymentResult.rows[0],
                        transaction_description: transaction.description
                    });
                } catch (insertError) {
                    console.error(`Lỗi khi thêm giao dịch ${transactionId}:`, insertError);

                    // Nếu lỗi là do transaction_id quá dài, thử tạo lại với ID ngắn hơn
                    if (insertError.code === '22001') {
                        console.log(`Transaction ID quá dài (${transactionId.length} ký tự), tạo lại với ID ngắn hơn...`);

                        // Tạo transaction_id ngắn hơn chỉ với timestamp và hash ngắn
                        const fallbackContent = `${transaction.transactionDate}_${transaction.amount}`;
                        const shortHash = crypto.createHash('md5').update(fallbackContent).digest('hex').substring(0, 8);
                        const shortTransactionId = `${Date.now()}_${shortHash}`;

                        console.log(`Thử lại với transaction ID ngắn: ${shortTransactionId} (${shortTransactionId.length} ký tự)`);

                        try {
                            const retryPaymentResult = await pool.query(`
                                INSERT INTO purchase_order_payments (
                                    purchase_order_id, amount, payment_date, transaction_id, verified_by
                                )
                                VALUES ($1, $2, $3, $4, $5)
                                RETURNING *
                            `, [
                                purchaseOrderId, amount, new Date(transaction.transactionDate),
                                shortTransactionId, req.admin.id
                            ]);

                            console.log(`Đã thêm giao dịch ${shortTransactionId} vào bảng purchase_order_payments với ID ${retryPaymentResult.rows[0].id}`);

                            newPayments.push({
                                ...retryPaymentResult.rows[0],
                                transaction_description: transaction.description
                            });
                        } catch (retryError) {
                            console.error(`Lỗi khi thử lại với transaction ID ngắn:`, retryError);
                            // Bỏ qua giao dịch này và tiếp tục với giao dịch khác
                            console.log(`Bỏ qua giao dịch này và tiếp tục xử lý...`);
                        }
                    } else {
                        // Nếu là lỗi khác, bỏ qua giao dịch này
                        console.log(`Bỏ qua giao dịch này do lỗi: ${insertError.message}`);
                    }
                }
            } else {
                console.log(`Giao dịch ${transactionId} đã được ghi nhận trước đó với ID ${existingPayment.rows[0].id}`);
            }
        }

        console.log(`Tổng số giao dịch mới: ${newPayments.length}, Tổng số tiền mới: ${totalPaid}`);

        // Lấy tổng số tiền đã thanh toán từ database
        const paymentsResult = await pool.query(
            'SELECT SUM(amount) as total_paid FROM purchase_order_payments WHERE purchase_order_id = $1',
            [purchaseOrderId]
        );

        const totalPaidFromDB = parseFloat(paymentsResult.rows[0].total_paid) || 0;
        console.log(`DEBUG - totalPaidFromDB (raw): ${paymentsResult.rows[0].total_paid}, type: ${typeof paymentsResult.rows[0].total_paid}`);
        console.log(`DEBUG - totalPaidFromDB (parsed): ${totalPaidFromDB}, type: ${typeof totalPaidFromDB}`);
        
        // Tính số tiền còn lại với xử lý tolerance
        let remainingAmount = parseFloat((purchaseOrder.total_amount * purchaseOrder.exchange_rate - totalPaidFromDB).toFixed(2));
        
        // Xử lý tolerance: nếu còn lại <= 1 VNĐ, coi như đã thanh toán đủ
        const tolerance = 1.0;
        if (remainingAmount <= tolerance && remainingAmount > 0) {
            console.log(`Áp dụng tolerance trong kiểm tra thanh toán: Số tiền còn lại ${remainingAmount} VNĐ <= ${tolerance} VNĐ, coi như đã thanh toán đủ`);
            remainingAmount = 0;
        } else if (remainingAmount < 0) {
            remainingAmount = 0; // Không cho phép số âm
        }

        // Cập nhật trạng thái thanh toán
        let paymentStatus = 'pending';

        // Lấy thông tin về số tiền cọc và chuyển đổi sang số
        const depositAmount = parseFloat(purchaseOrder.deposit_amount) || 0;

        // Log chi tiết để debug
        console.log('DEBUG - Thông tin chi tiết:');
        console.log(`- deposit_amount: ${depositAmount}`);
        console.log(`- deposit_percentage: ${purchaseOrder.deposit_percentage}%`);
        console.log(`- totalPaidFromDB: ${totalPaidFromDB}`);
        console.log(`- Điều kiện đã cọc: totalPaidFromDB (${totalPaidFromDB}) >= depositAmount (${depositAmount}) = ${totalPaidFromDB >= depositAmount}`);

        // Kiểm tra đặc biệt cho trường hợp tỷ lệ cọc là 80%
        if (purchaseOrder.deposit_percentage == 80) {
            const expectedDeposit = parseFloat(((purchaseOrder.total_amount * purchaseOrder.exchange_rate) * 0.8).toFixed(2));
            console.log(`- Trường hợp đặc biệt - Tỷ lệ cọc 80%:`);
            console.log(`  + Số tiền cọc tính lại: ${expectedDeposit}`);
            console.log(`  + So sánh với số đã thanh toán: ${totalPaidFromDB} >= ${expectedDeposit} = ${totalPaidFromDB >= expectedDeposit}`);
        }

        // Kiểm tra trạng thái thanh toán với dung sai nhỏ (0.01) để tránh lỗi làm tròn số
        const epsilon = 0.01; // Dung sai nhỏ để so sánh số thực

        // Trường hợp đặc biệt cho tỷ lệ cọc 80%
        if (purchaseOrder.deposit_percentage == 80) {
            const expectedDeposit = parseFloat(((purchaseOrder.total_amount * purchaseOrder.exchange_rate) * 0.8).toFixed(2));

            if (remainingAmount <= epsilon) {
                // Nếu đã thanh toán đủ toàn bộ số tiền (với dung sai)
                paymentStatus = 'completed';
                console.log('-> Đã thanh toán đủ toàn bộ số tiền -> completed');
            } else if (totalPaidFromDB + epsilon >= expectedDeposit) {
                // Nếu đã thanh toán đủ tiền cọc nhưng chưa thanh toán đủ toàn bộ (với dung sai)
                paymentStatus = 'deposited';
                console.log('-> [Trường hợp 80%] Đã thanh toán đủ tiền cọc -> deposited');
            } else {
                // Nếu chưa thanh toán đủ tiền cọc
                paymentStatus = 'pending';
                console.log('-> Chưa thanh toán đủ tiền cọc -> pending');
            }
        } else {
            // Xử lý các trường hợp thông thường
            if (remainingAmount <= epsilon) {
                // Nếu đã thanh toán đủ toàn bộ số tiền (với dung sai)
                paymentStatus = 'completed';
                console.log('-> Đã thanh toán đủ toàn bộ số tiền -> completed');
            } else if (totalPaidFromDB + epsilon >= depositAmount) {
                // Nếu đã thanh toán đủ tiền cọc nhưng chưa thanh toán đủ toàn bộ (với dung sai)
                paymentStatus = 'deposited';
                console.log('-> Đã thanh toán đủ tiền cọc -> deposited');
            } else {
                // Nếu chưa thanh toán đủ tiền cọc
                paymentStatus = 'pending';
                console.log('-> Chưa thanh toán đủ tiền cọc -> pending');
            }
        }

        // Giải pháp dự phòng: Nếu đã thanh toán ít nhất 80% tổng số tiền nhưng chưa thanh toán đủ, cũng đánh dấu là đã cọc
        if (paymentStatus === 'pending') {
            const totalAmount = purchaseOrder.total_amount * purchaseOrder.exchange_rate;
            const paymentPercentage = (totalPaidFromDB / totalAmount) * 100;

            console.log(`- Phần trăm đã thanh toán: ${paymentPercentage.toFixed(2)}%`);

            if (paymentPercentage >= 80) {
                paymentStatus = 'deposited';
                console.log('-> [Giải pháp dự phòng] Đã thanh toán ít nhất 80% tổng số tiền -> deposited');
            }
        }

        // Kiểm tra xem cơ sở dữ liệu đã được cập nhật để chấp nhận giá trị 'deposited' chưa
        // Nếu chưa, sử dụng giá trị tạm thời là 'pending' với một ghi chú
        let dbPaymentStatus = paymentStatus;
        let isDeposited = false;

        if (paymentStatus === 'deposited') {
            try {
                // Thử kiểm tra xem ràng buộc đã được cập nhật chưa
                const checkResult = await pool.query(`
                    SELECT COUNT(*) FROM pg_constraint
                    WHERE conname = 'purchase_orders_payment_status_check'
                    AND pg_get_constraintdef(oid) LIKE '%deposited%'
                `);

                if (parseInt(checkResult.rows[0].count) === 0) {
                    console.log('CẢNH BÁO: Ràng buộc cơ sở dữ liệu chưa được cập nhật để chấp nhận giá trị "deposited"');
                    console.log('Sử dụng giá trị "pending" tạm thời. Vui lòng chạy script update_payment_status_constraint.sql');
                    dbPaymentStatus = 'pending';
                    isDeposited = true; // Đánh dấu là đã cọc nhưng không thể lưu vào DB
                }
            } catch (error) {
                console.error('Lỗi khi kiểm tra ràng buộc:', error);
                dbPaymentStatus = 'pending';
                isDeposited = true;
            }
        }

        // Log thêm thông tin về tiền cọc
        console.log(`- Số tiền cọc cần thanh toán: ${depositAmount}`);
        console.log(`- Trạng thái thanh toán thực tế: ${paymentStatus} (pending: chưa cọc, deposited: đã cọc, completed: đã thanh toán đủ)`);
        if (isDeposited) {
            console.log(`- Trạng thái thanh toán lưu vào DB: ${dbPaymentStatus} (tạm thời do ràng buộc DB)`);
        }

        // Cập nhật đơn mua hộ
        try {
            // Thử cập nhật với cột paid_amount và trạng thái thanh toán đã xử lý
            await pool.query(`
                UPDATE purchase_orders
                SET payment_status = $1, paid_amount = $2, remaining_amount = $3
                WHERE id = $4
            `, [
                dbPaymentStatus, totalPaidFromDB, Math.max(0, remainingAmount), purchaseOrderId
            ]);

            // Nếu đã cọc nhưng không thể lưu trạng thái 'deposited', lưu thông tin vào note
            if (isDeposited) {
                // Thêm ghi chú về trạng thái thực tế
                await pool.query(`
                    UPDATE purchase_orders
                    SET note = CASE
                        WHEN note IS NULL OR note = '' THEN '[ĐÃ CỌC] Đã thanh toán đủ tiền cọc'
                        ELSE note || ' | [ĐÃ CỌC] Đã thanh toán đủ tiền cọc'
                    END
                    WHERE id = $1
                `, [purchaseOrderId]);

                console.log('Đã thêm ghi chú "[ĐÃ CỌC]" vào đơn hàng để đánh dấu trạng thái thực tế');
            }
        } catch (updateError) {
            // Nếu lỗi liên quan đến cột paid_amount không tồn tại
            if (updateError.code === '42703' && updateError.message.includes('paid_amount')) {
                console.log('Cột paid_amount chưa tồn tại, chỉ cập nhật payment_status và remaining_amount');
                // Chỉ cập nhật payment_status và remaining_amount
                await pool.query(`
                    UPDATE purchase_orders
                    SET payment_status = $1, remaining_amount = $2
                    WHERE id = $3
                `, [
                    dbPaymentStatus, Math.max(0, remainingAmount), purchaseOrderId
                ]);

                // Nếu đã cọc nhưng không thể lưu trạng thái 'deposited', lưu thông tin vào note
                if (isDeposited) {
                    // Thêm ghi chú về trạng thái thực tế
                    await pool.query(`
                        UPDATE purchase_orders
                        SET note = CASE
                            WHEN note IS NULL OR note = '' THEN '[ĐÃ CỌC] Đã thanh toán đủ tiền cọc'
                            ELSE note || ' | [ĐÃ CỌC] Đã thanh toán đủ tiền cọc'
                        END
                        WHERE id = $1
                    `, [purchaseOrderId]);

                    console.log('Đã thêm ghi chú "[ĐÃ CỌC]" vào đơn hàng để đánh dấu trạng thái thực tế');
                }

                // Thông báo cho admin biết cần thêm cột paid_amount
                console.log('CẢNH BÁO: Cần thêm cột paid_amount vào bảng purchase_orders. Vui lòng chạy script add_paid_amount_column.sql');
            } else if (updateError.code === '23514' && updateError.constraint === 'purchase_orders_payment_status_check') {
                // Nếu lỗi là do ràng buộc payment_status
                console.log('Lỗi ràng buộc payment_status, sử dụng giá trị "pending" thay thế');

                // Cập nhật với trạng thái 'pending'
                await pool.query(`
                    UPDATE purchase_orders
                    SET payment_status = 'pending', paid_amount = $1, remaining_amount = $2
                    WHERE id = $3
                `, [
                    totalPaidFromDB, Math.max(0, remainingAmount), purchaseOrderId
                ]);

                // Thêm ghi chú về trạng thái thực tế
                await pool.query(`
                    UPDATE purchase_orders
                    SET note = CASE
                        WHEN note IS NULL OR note = '' THEN '[ĐÃ CỌC] Đã thanh toán đủ tiền cọc'
                        ELSE note || ' | [ĐÃ CỌC] Đã thanh toán đủ tiền cọc'
                    END
                    WHERE id = $1
                `, [purchaseOrderId]);

                console.log('Đã thêm ghi chú "[ĐÃ CỌC]" vào đơn hàng để đánh dấu trạng thái thực tế');
                console.log('CẢNH BÁO: Cần cập nhật ràng buộc payment_status. Vui lòng chạy script update_payment_status_constraint.sql');
            } else {
                // Nếu là lỗi khác, ném lại lỗi
                throw updateError;
            }
        }

        // Lấy thông tin đơn mua hộ đã cập nhật
        const updatedPurchaseOrderResult = await pool.query(
            'SELECT * FROM purchase_orders WHERE id = $1',
            [purchaseOrderId]
        );

        // Log chi tiết về tính toán trạng thái thanh toán
        console.log('Chi tiết tính toán trạng thái thanh toán:');
        console.log(`- Tổng số tiền đơn mua hộ: ${purchaseOrder.total_amount * purchaseOrder.exchange_rate}`);
        console.log(`- Số tiền đã thanh toán trước đó: ${purchaseOrder.paid_amount || 0}`);
        console.log(`- Số tiền thanh toán mới: ${totalPaid}`);
        console.log(`- Tổng số tiền đã thanh toán: ${totalPaidFromDB}`);
        console.log(`- Số tiền còn lại: ${remainingAmount}`);
        console.log(`- Trạng thái thanh toán mới: ${paymentStatus}`);

        res.json({
            message: newPayments.length > 0
                ? `Đã tìm thấy ${newPayments.length} giao dịch thanh toán mới`
                : 'Không có giao dịch thanh toán mới',
            status: paymentStatus,
            purchaseOrder: updatedPurchaseOrderResult.rows[0],
            newPayments,
            paid_amount: totalPaidFromDB,
            remaining_amount: Math.max(0, remainingAmount)
        });
    } catch (error) {
        console.error('Lỗi khi kiểm tra thanh toán đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi kiểm tra thanh toán đơn mua hộ' });
    }
});

/**
 * @api {post} /api/admin/purchase-orders/:id/manual-payment Xác nhận thanh toán thủ công
 * @apiName ManualPaymentPurchaseOrder
 * @apiGroup PurchaseOrder
 * @apiPermission admin/staff
 *
 * @apiParam {Number} id ID của đơn mua hộ
 * @apiParam {Number} amount Số tiền thanh toán
 * @apiParam {String} payment_date Ngày thanh toán (YYYY-MM-DD)
 * @apiParam {String} [note] Ghi chú thanh toán
 *
 * @apiSuccess {Object} result Kết quả xác nhận thanh toán
 */
router.post('/:id/manual-payment', authenticateStaffToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const purchaseOrderId = req.params.id;
        const { amount, payment_date, note } = req.body;

        // Xác định ID của người xác nhận thanh toán
        let adminId;
        if (req.admin && req.admin.id) {
            adminId = req.admin.id;
        } else if (req.staff && req.staff.id) {
            adminId = req.staff.id;
        } else {
            await client.query('ROLLBACK');
            return res.status(401).json({ message: 'Không thể xác định người dùng xác nhận thanh toán' });
        }

        // Validation
        if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
            return res.status(400).json({ message: 'Vui lòng nhập số tiền thanh toán hợp lệ' });
        }

        if (!payment_date) {
            return res.status(400).json({ message: 'Vui lòng nhập ngày thanh toán' });
        }

        // Kiểm tra đơn mua hộ có tồn tại không
        const purchaseOrderResult = await client.query(
            'SELECT po.*, u.username FROM purchase_orders po JOIN users u ON po.user_id = u.id WHERE po.id = $1',
            [purchaseOrderId]
        );

        if (purchaseOrderResult.rows.length === 0) {
            await client.query('ROLLBACK');
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ' });
        }

        const purchaseOrder = purchaseOrderResult.rows[0];
        const paymentAmount = parseFloat(amount);

        // Tạo transaction_id đặc biệt cho thanh toán thủ công
        // Bao gồm cả ghi chú trong transaction_id nếu có
        const transactionId = note
            ? `MANUAL-${Date.now()}-${adminId}-NOTE:${note.substring(0, 50)}`
            : `MANUAL-${Date.now()}-${adminId}`;

        // Thêm vào bảng purchase_order_payments
        // Bảng purchase_order_payments không có cột note, nên chỉ insert các cột cần thiết
        // Ghi chú sẽ được lưu trong transaction_id
        const paymentResult = await client.query(`
            INSERT INTO purchase_order_payments (
                purchase_order_id, amount, payment_date, transaction_id, verified_by
            )
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
        `, [
            purchaseOrderId, paymentAmount, payment_date, transactionId, adminId
        ]);

        console.log(`Đã thêm thanh toán thủ công ${transactionId} vào bảng purchase_order_payments với ID ${paymentResult.rows[0].id}`);

        // Lấy tổng số tiền đã thanh toán từ database
        const paymentsResult = await client.query(
            'SELECT SUM(amount) as total_paid FROM purchase_order_payments WHERE purchase_order_id = $1',
            [purchaseOrderId]
        );

        const totalPaidFromDB = parseFloat(paymentsResult.rows[0].total_paid) || 0;
        
        // Tính số tiền còn lại với xử lý tolerance
        let remainingAmount = parseFloat((purchaseOrder.total_amount * purchaseOrder.exchange_rate - totalPaidFromDB).toFixed(2));
        
        // Xử lý tolerance: nếu còn lại <= 1 VNĐ, coi như đã thanh toán đủ
        const tolerance = 1.0;
        if (remainingAmount <= tolerance && remainingAmount > 0) {
            console.log(`Áp dụng tolerance trong thanh toán thủ công: Số tiền còn lại ${remainingAmount} VNĐ <= ${tolerance} VNĐ, coi như đã thanh toán đủ`);
            remainingAmount = 0;
        } else if (remainingAmount < 0) {
            remainingAmount = 0; // Không cho phép số âm
        }

        // Cập nhật trạng thái thanh toán
        let paymentStatus = 'pending';
        const epsilon = 0.01; // Dung sai cho việc so sánh số thực

        // Lấy thông tin về số tiền cọc
        const depositAmount = parseFloat(purchaseOrder.deposit_amount) || 0;

        console.log(`DEBUG - Tổng số tiền đã thanh toán: ${totalPaidFromDB}`);
        console.log(`DEBUG - Số tiền còn lại: ${remainingAmount}`);
        console.log(`DEBUG - Số tiền cọc: ${depositAmount}`);

        if (remainingAmount <= epsilon) {
            // Nếu đã thanh toán đủ toàn bộ số tiền (với dung sai)
            paymentStatus = 'completed';
            console.log('-> Đã thanh toán đủ toàn bộ số tiền -> completed');
        } else if (totalPaidFromDB + epsilon >= depositAmount) {
            // Nếu đã thanh toán đủ tiền cọc nhưng chưa thanh toán đủ toàn bộ (với dung sai)
            paymentStatus = 'deposited';
            console.log('-> Đã thanh toán đủ tiền cọc -> deposited');
        } else {
            // Nếu chưa thanh toán đủ tiền cọc
            paymentStatus = 'pending';
            console.log('-> Chưa thanh toán đủ tiền cọc -> pending');
        }

        // Cập nhật trạng thái thanh toán trong bảng purchase_orders
        const updateResult = await client.query(`
            UPDATE purchase_orders
            SET payment_status = $1, paid_amount = $2, remaining_amount = $3, updated_at = CURRENT_TIMESTAMP
            WHERE id = $4
            RETURNING *
        `, [paymentStatus, totalPaidFromDB, Math.max(0, remainingAmount), purchaseOrderId]);

        await client.query('COMMIT');

        console.log(`Đã cập nhật trạng thái thanh toán đơn mua hộ ${purchaseOrder.order_code} thành ${paymentStatus}`);

        res.json({
            message: 'Đã xác nhận thanh toán thủ công thành công',
            status: paymentStatus,
            purchaseOrder: updateResult.rows[0],
            payment: paymentResult.rows[0],
            paid_amount: totalPaidFromDB,
            remaining_amount: Math.max(0, remainingAmount)
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('Lỗi khi xác nhận thanh toán thủ công:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi xác nhận thanh toán thủ công' });
    } finally {
        client.release();
    }
});

/**
 * @api {delete} /api/admin/purchase-orders/:id Xóa đơn mua hộ (Soft Delete)
 * @apiName DeletePurchaseOrder
 * @apiGroup PurchaseOrder
 * @apiPermission admin
 *
 * @apiParam {Number} id ID của đơn mua hộ
 *
 * @apiSuccess {Object} result Kết quả xóa đơn mua hộ
 */
router.delete('/:id', authenticateAdminToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const purchaseOrderId = req.params.id;
        const adminId = req.admin.id;

        // Kiểm tra đơn mua hộ có tồn tại và chưa bị xóa không
        const purchaseOrderResult = await client.query(
            'SELECT * FROM purchase_orders WHERE id = $1 AND deleted_at IS NULL',
            [purchaseOrderId]
        );

        if (purchaseOrderResult.rows.length === 0) {
            await client.query('ROLLBACK');
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ hoặc đơn hàng đã bị xóa' });
        }

        const orderCode = purchaseOrderResult.rows[0].order_code;

        // Soft delete đơn mua hộ
        await client.query(`
            UPDATE purchase_orders
            SET
                deleted_at = CURRENT_TIMESTAMP,
                deleted_by = $1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $2 AND deleted_at IS NULL
        `, [adminId, purchaseOrderId]);

        await client.query('COMMIT');

        res.json({
            message: `Đã xóa đơn mua hộ ${orderCode} thành công`,
            id: purchaseOrderId,
            deleted_at: new Date().toISOString(),
            note: 'Đơn hàng đã được soft delete. Có thể khôi phục nếu cần.'
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('Lỗi khi xóa đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi xóa đơn mua hộ' });
    } finally {
        client.release();
    }
});

/**
 * @api {post} /api/admin/purchase-orders/:id/delete Xóa đơn mua hộ (phương thức POST - Soft Delete)
 * @apiName DeletePurchaseOrderPost
 * @apiGroup PurchaseOrder
 * @apiPermission admin
 *
 * @apiParam {Number} id ID của đơn mua hộ
 *
 * @apiSuccess {Object} result Kết quả xóa đơn mua hộ
 */
router.post('/:id/delete', authenticateAdminToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const purchaseOrderId = req.params.id;
        const adminId = req.admin.id;

        // Kiểm tra đơn mua hộ có tồn tại và chưa bị xóa không
        const purchaseOrderResult = await client.query(
            'SELECT * FROM purchase_orders WHERE id = $1 AND deleted_at IS NULL',
            [purchaseOrderId]
        );

        if (purchaseOrderResult.rows.length === 0) {
            await client.query('ROLLBACK');
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ hoặc đơn hàng đã bị xóa' });
        }

        const orderCode = purchaseOrderResult.rows[0].order_code;

        // Soft delete đơn mua hộ
        await client.query(`
            UPDATE purchase_orders
            SET
                deleted_at = CURRENT_TIMESTAMP,
                deleted_by = $1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $2 AND deleted_at IS NULL
        `, [adminId, purchaseOrderId]);

        await client.query('COMMIT');

        res.json({
            message: `Đã xóa đơn mua hộ ${orderCode} thành công`,
            id: purchaseOrderId,
            deleted_at: new Date().toISOString(),
            note: 'Đơn hàng đã được soft delete. Có thể khôi phục nếu cần.'
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('Lỗi khi xóa đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi xóa đơn mua hộ' });
    } finally {
        client.release();
    }
});

/**
 * @api {post} /api/admin/purchase-orders/:id/sync-tracking-status Đồng bộ trạng thái tracking cho đơn mua hộ
 * @apiName SyncPurchaseOrderTrackingStatus
 * @apiGroup PurchaseOrder
 * @apiPermission admin/staff
 *
 * @apiParam {Number} id ID của đơn mua hộ
 *
 * @apiSuccess {Object} result Kết quả đồng bộ trạng thái tracking
 */
router.post('/:id/sync-tracking-status', authenticateStaffToken, async (req, res) => {
    try {
        const purchaseOrderId = req.params.id;

        // Kiểm tra đơn mua hộ có tồn tại không
        const purchaseOrderResult = await pool.query(
            'SELECT * FROM purchase_orders WHERE id = $1',
            [purchaseOrderId]
        );

        if (purchaseOrderResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ' });
        }

        // Đồng bộ trạng thái tracking
        const orderStatus = await syncPurchaseOrderTrackingStatus(purchaseOrderId);

        res.json({
            message: 'Đã đồng bộ trạng thái tracking thành công',
            purchase_order_id: purchaseOrderId,
            order_status: orderStatus
        });
    } catch (error) {
        console.error('Lỗi khi đồng bộ trạng thái tracking cho đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi đồng bộ trạng thái tracking cho đơn mua hộ' });
    }
});

/**
 * @api {get} /api/purchase-orders/lookup/username/:username Tìm kiếm đơn mua hộ theo username
 * @apiName LookupPurchaseOrdersByUsername
 * @apiGroup PurchaseOrder
 * @apiPermission public
 *
 * @apiParam {String} username Username của khách hàng
 *
 * @apiSuccess {Array} purchaseOrders Danh sách đơn mua hộ
 */
router.get('/lookup/username/:username', async (req, res) => {
    try {
        const { username } = req.params;

        // Tìm đơn mua hộ theo username
        const ordersResult = await pool.query(`
            SELECT po.*, u.username as user_username, s.fullname as staff_name
            FROM purchase_orders po
            LEFT JOIN users u ON po.user_id = u.id
            LEFT JOIN staff s ON po.staff_id = s.id
            WHERE u.username = $1 AND po.deleted_at IS NULL
            ORDER BY po.created_at DESC
        `, [username]);

        if (ordersResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ nào cho khách hàng này' });
        }

        const purchaseOrders = ordersResult.rows;
        const orderIds = purchaseOrders.map(order => order.id);

        // Lấy danh sách sản phẩm cho tất cả đơn hàng
        const itemsResult = await pool.query(`
            SELECT poi.*, pot.tracking_number, t.status as tracking_status
            FROM purchase_order_items poi
            LEFT JOIN purchase_order_tracking pot ON poi.id = pot.purchase_order_item_id
            LEFT JOIN tracking t ON pot.tracking_number = t.number
            WHERE poi.purchase_order_id = ANY($1)
            ORDER BY poi.purchase_order_id, poi.id
        `, [orderIds]);

        // Tạo map để lưu trữ sản phẩm theo ID đơn mua hộ
        const itemsByOrderId = {};
        itemsResult.rows.forEach(item => {
            if (!itemsByOrderId[item.purchase_order_id]) {
                itemsByOrderId[item.purchase_order_id] = [];
            }
            itemsByOrderId[item.purchase_order_id].push(item);
        });

        // Tính toán lại các giá trị tài chính và gắn items cho mỗi đơn hàng
        const processedOrders = purchaseOrders.map(purchaseOrder => {
            const items = itemsByOrderId[purchaseOrder.id] || [];
            
            // Tính toán lại các giá trị tài chính cho tra cứu công khai
            const totalAmount = purchaseOrder.total_amount * purchaseOrder.exchange_rate;
            const paidAmount = purchaseOrder.paid_amount || 0;
            const calculatedRemainingAmount = Math.max(0, totalAmount - paidAmount);
            const calculatedDepositAmount = (totalAmount * purchaseOrder.deposit_percentage) / 100;

            // Chỉ trả về thông tin cần thiết cho tra cứu công khai
            const publicOrderInfo = {
                id: purchaseOrder.id,
                order_code: purchaseOrder.order_code,
                user_username: purchaseOrder.user_username,
                staff_name: purchaseOrder.staff_name,
                total_amount: purchaseOrder.total_amount,
                exchange_rate: purchaseOrder.exchange_rate,
                paid_amount: paidAmount,
                remaining_amount: calculatedRemainingAmount,
                payment_status: purchaseOrder.payment_status,
                status: purchaseOrder.status,
                created_at: purchaseOrder.created_at,
                deposit_amount: calculatedDepositAmount,
                deposit_percentage: purchaseOrder.deposit_percentage
            };

            // Chỉ trả về thông tin cơ bản của sản phẩm (không bao gồm thông tin mua hàng nội bộ)
            const publicItems = items.map(item => ({
                id: item.id,
                product_name: item.product_name,
                product_variant: item.product_variant,
                quantity: item.quantity,
                purchased_quantity: item.purchased_quantity,
                price: item.price,
                exchange_rate: item.exchange_rate,
                tracking_number: item.tracking_number,
                tracking_status: item.tracking_status
                // Không bao gồm: purchase_price, purchase_exchange_rate (thông tin nội bộ)
            }));

            return {
                purchaseOrder: publicOrderInfo,
                items: publicItems
            };
        });

        res.json({
            message: `Tìm thấy ${processedOrders.length} đơn hàng cho khách hàng ${username}`,
            orders: processedOrders,
            total_orders: processedOrders.length
        });
    } catch (error) {
        console.error('Lỗi khi tìm kiếm đơn mua hộ theo username:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi tìm kiếm đơn mua hộ' });
    }
});

/**
 * @api {get} /api/purchase-orders/lookup/:orderCode Tra cứu đơn mua hộ công khai
 * @apiName LookupPurchaseOrder
 * @apiGroup PurchaseOrder
 * @apiPermission public
 *
 * @apiParam {String} orderCode Mã đơn mua hộ
 *
 * @apiSuccess {Object} purchaseOrder Thông tin đơn mua hộ
 * @apiSuccess {Array} items Danh sách sản phẩm
 */
router.get('/lookup/:orderCode', async (req, res) => {
    try {
        const { orderCode } = req.params;

        // Tìm đơn mua hộ theo mã đơn
        const orderResult = await pool.query(`
            SELECT po.*, u.username as user_username, s.fullname as staff_name
            FROM purchase_orders po
            LEFT JOIN users u ON po.user_id = u.id
            LEFT JOIN staff s ON po.staff_id = s.id
            WHERE po.order_code = $1
        `, [orderCode]);

        if (orderResult.rows.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ' });
        }

        const purchaseOrder = orderResult.rows[0];

        // Lấy danh sách sản phẩm
        const itemsResult = await pool.query(`
            SELECT poi.*, pot.tracking_number, t.status as tracking_status
            FROM purchase_order_items poi
            LEFT JOIN purchase_order_tracking pot ON poi.id = pot.purchase_order_item_id
            LEFT JOIN tracking t ON pot.tracking_number = t.number
            WHERE poi.purchase_order_id = $1
            ORDER BY poi.id
        `, [purchaseOrder.id]);

        const items = itemsResult.rows;

        // Tính toán lại các giá trị tài chính cho tra cứu công khai
        const totalAmount = purchaseOrder.total_amount * purchaseOrder.exchange_rate;
        const paidAmount = purchaseOrder.paid_amount || 0;
        const calculatedRemainingAmount = Math.max(0, totalAmount - paidAmount);
        const calculatedDepositAmount = (totalAmount * purchaseOrder.deposit_percentage) / 100;

        // Chỉ trả về thông tin cần thiết cho tra cứu công khai
        const publicOrderInfo = {
            id: purchaseOrder.id,
            order_code: purchaseOrder.order_code,
            user_username: purchaseOrder.user_username,
            staff_name: purchaseOrder.staff_name,
            total_amount: purchaseOrder.total_amount,
            exchange_rate: purchaseOrder.exchange_rate,
            paid_amount: paidAmount,
            remaining_amount: calculatedRemainingAmount,
            payment_status: purchaseOrder.payment_status,
            status: purchaseOrder.status,
            created_at: purchaseOrder.created_at,
            deposit_amount: calculatedDepositAmount,
            deposit_percentage: purchaseOrder.deposit_percentage
        };

        // Chỉ trả về thông tin cơ bản của sản phẩm (không bao gồm thông tin mua hàng nội bộ)
        const publicItems = items.map(item => ({
            id: item.id,
            product_name: item.product_name,
            product_variant: item.product_variant,
            quantity: item.quantity,
            purchased_quantity: item.purchased_quantity,
            price: item.price,
            exchange_rate: item.exchange_rate,
            tracking_number: item.tracking_number,
            tracking_status: item.tracking_status
            // Không bao gồm: purchase_price, purchase_exchange_rate (thông tin nội bộ)
        }));

        res.json({
            purchaseOrder: publicOrderInfo,
            items: publicItems
        });
    } catch (error) {
        console.error('Lỗi khi tra cứu đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi tra cứu đơn mua hộ' });
    }
});

/**
 * @api {post} /api/admin/purchase-orders/:id/restore Khôi phục đơn mua hộ đã xóa
 * @apiName RestorePurchaseOrder
 * @apiGroup PurchaseOrder
 * @apiPermission admin
 *
 * @apiParam {Number} id ID của đơn mua hộ
 *
 * @apiSuccess {Object} result Kết quả khôi phục đơn mua hộ
 */
router.post('/:id/restore', authenticateAdminToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const purchaseOrderId = req.params.id;

        // Kiểm tra đơn mua hộ có tồn tại và đã bị xóa không
        const purchaseOrderResult = await client.query(
            'SELECT * FROM purchase_orders WHERE id = $1 AND deleted_at IS NOT NULL',
            [purchaseOrderId]
        );

        if (purchaseOrderResult.rows.length === 0) {
            await client.query('ROLLBACK');
            return res.status(404).json({ message: 'Không tìm thấy đơn mua hộ đã xóa' });
        }

        const orderCode = purchaseOrderResult.rows[0].order_code;

        // Kiểm tra xem order_code có bị trùng với đơn hàng khác không
        const duplicateCheck = await client.query(
            'SELECT id FROM purchase_orders WHERE order_code = $1 AND deleted_at IS NULL',
            [orderCode]
        );

        if (duplicateCheck.rows.length > 0) {
            await client.query('ROLLBACK');
            return res.status(400).json({
                message: `Không thể khôi phục đơn hàng ${orderCode} vì mã đơn hàng đã được sử dụng cho đơn hàng khác`
            });
        }

        // Khôi phục đơn mua hộ
        await client.query(`
            UPDATE purchase_orders
            SET
                deleted_at = NULL,
                deleted_by = NULL,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $1 AND deleted_at IS NOT NULL
        `, [purchaseOrderId]);

        await client.query('COMMIT');

        res.json({
            message: `Đã khôi phục đơn mua hộ ${orderCode} thành công`,
            id: purchaseOrderId,
            restored_at: new Date().toISOString()
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('Lỗi khi khôi phục đơn mua hộ:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi khôi phục đơn mua hộ' });
    } finally {
        client.release();
    }
});

/**
 * @api {get} /api/admin/purchase-orders/deleted Lấy danh sách đơn mua hộ đã xóa
 * @apiName GetDeletedPurchaseOrders
 * @apiGroup PurchaseOrder
 * @apiPermission admin
 *
 * @apiSuccess {Array} purchaseOrders Danh sách đơn mua hộ đã xóa
 */
router.get('/deleted', authenticateAdminToken, async (req, res) => {
    try {
        const result = await pool.query(`
            SELECT
                po.*,
                u.username,
                s.name as staff_name,
                au.username as deleted_by_username
            FROM purchase_orders po
            LEFT JOIN users u ON po.user_id = u.id
            LEFT JOIN staff s ON po.staff_id = s.id
            LEFT JOIN admin_users au ON po.deleted_by = au.id
            WHERE po.deleted_at IS NOT NULL
            ORDER BY po.deleted_at DESC
        `);

        res.json({
            message: 'Lấy danh sách đơn mua hộ đã xóa thành công',
            purchaseOrders: result.rows
        });
    } catch (error) {
        console.error('Lỗi khi lấy danh sách đơn mua hộ đã xóa:', error);
        res.status(500).json({ message: 'Có lỗi xảy ra khi lấy danh sách đơn mua hộ đã xóa' });
    }
});

module.exports = router;
